<!-- src/routes/(public)/onboarding/+page.svelte -->
<script lang="ts">
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		List,
		ListInput,
		Button,
		ListItem
	} from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import type { PageData } from './$types';
	import ChipGroup from '$lib/components/ui/ChipGroup.svelte';
	import RangeSlider from '$lib/components/ui/RangeSlider.svelte';
	import HeightRangeSlider from '$lib/components/ui/HeightRangeSlider.svelte';
	import WeightRangeSlider from '$lib/components/ui/WeightRangeSlider.svelte';
	import CountrySelector from '$lib/components/ui/CountrySelector.svelte';

	export let data: PageData;

	const { form, errors, submitting, languageCode, bodyTypeOptions } = data;

	// Prepare items for the ChipGroup
	const bodyTypeItems = bodyTypeOptions.map((value) => ({
		value,
		label: m[`data_body_type_${value}`]()
	}));
</script>

<Page>
	<Navbar title={m.routes_onboarding_title()} />

	<form method="POST">
		<BlockTitle>{m.routes_onboarding_basic_profile_title()}</BlockTitle>
		<Block strong inset>
			<p class="text-sm text-gray-500 mb-4">{m.routes_onboarding_basic_profile_description()}</p>

			<List strong inset>
				<!-- Nickname -->
				<ListInput
					label={m.routes_onboarding_label_nickname()}
					type="text"
					placeholder={m.routes_onboarding_placeholder_nickname()}
					name="nickname"
					bind:value={$form.nickname}
					error={$errors.nickname ? $errors.nickname[0] : ''}
					errorMessage={$errors.nickname ? $errors.nickname[0] : ''}
				/>
			</List>
		</Block>

		<BlockTitle>{m.routes_onboarding_physical_chars_title()}</BlockTitle>
		<List strong inset>
			<!-- Body Type -->
			<ListItem
				title={m.routes_onboarding_label_body_type()}
				text={$form.bodyType ? m[`data_body_type_${$form.bodyType}`]() : m.common_placeholder_select()}
			/>
			<div class="p-4 pt-2">
				<ChipGroup items={bodyTypeItems} bind:value={$form.bodyType} />
				{#if $errors.bodyType}
					<p class="text-red-500 text-sm mt-2">{$errors.bodyType[0]}</p>
				{/if}
			</div>

			<!-- Age -->
			<RangeSlider
				label={m.routes_onboarding_label_age()}
				min={18}
				max={99}
				step={1}
				bind:value={$form.age}
			/>

			<!-- Height -->
			<HeightRangeSlider
				label={m.routes_onboarding_label_height()}
				bind:value={$form.heightCm}
				countryCode={$form.countryCode}
			/>

			<!-- Weight -->
			<WeightRangeSlider
				label={m.routes_onboarding_label_weight()}
				bind:value={$form.weightKg}
				countryCode={$form.countryCode}
			/>
		</List>

		<BlockTitle>{m.routes_onboarding_location_title()}</BlockTitle>
		<div class="p-4">
			<CountrySelector
				bind:countryCode={$form.countryCode}
				bind:provinceCode={$form.provinceCode}
				initialLanguage={languageCode}
			/>
		</div>
		<List strong inset>
			<!-- City -->
			<ListInput
				label={m.routes_onboarding_label_city()}
				type="text"
				placeholder={m.routes_onboarding_placeholder_city()}
				name="city"
				bind:value={$form.city}
				error={$errors.city ? $errors.city[0] : ''}
				errorMessage={$errors.city ? $errors.city[0] : ''}
			/>
		</List>

		<Block class="p-4">
			<Button large tonal type="submit" disabled={$submitting}>
				{#if $submitting}
					{m.common_state_saving()}
				{:else}
					{m.routes_onboarding_button_submit()}
				{/if}
			</Button>
		</Block>
	</form>
</Page>
