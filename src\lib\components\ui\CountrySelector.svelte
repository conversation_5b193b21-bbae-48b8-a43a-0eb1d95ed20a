<!-- src/lib/components/ui/CountrySelector.svelte -->
<script lang="ts">
	import { State, Country } from 'country-state-city';
	import type { ICountry, IState } from 'country-state-city';
	import Svelecte from 'svelecte';
	import * as m from '$lib/paraglide/messages';

	type Props = {
		countryCode?: string;
		provinceCode?: string;
		initialLanguage?: string;
	};

	let {
		countryCode = $bindable(),
		provinceCode = $bindable(),
		initialLanguage = 'en'
	}: Props = $props();

	const allCountries: ICountry[] = Country.getAllCountries();
	let statesOfCountry: IState[] = [];

	// Reactive statement to update states when countryCode changes
	$effect(() => {
		if (countryCode) {
			statesOfCountry = State.getStatesOfCountry(countryCode);
		} else {
			statesOfCountry = [];
		}
		// Clear province selection when country changes
		provinceCode = undefined;
	});

	// Set initial country based on language, if countryCode is not already set
	$effect.pre(() => {
		if (!countryCode && initialLanguage) {
			const country = allCountries.find(
				(c) => c.isoCode.toLowerCase() === initialLanguage.toLowerCase()
			);
			if (country) {
				countryCode = country.isoCode;
			}
		}
	});
</script>

<div class="space-y-4">
	<!-- Country Selector -->
	<div>
		<Svelecte
			options={allCountries}
			labelField="name"
			valueField="isoCode"
			placeholder={m.components_country_selector_placeholder_country()}
			bind:value={countryCode}
			searchable={true}
		/>
	</div>

	<!-- State/Province Selector -->
	<div>
		<Svelecte
			options={statesOfCountry}
			labelField="name"
			valueField="isoCode"
			placeholder={m.components_country_selector_placeholder_province()}
			bind:value={provinceCode}
			disabled={statesOfCountry.length === 0}
			searchable={true}
		/>
	</div>
</div>
