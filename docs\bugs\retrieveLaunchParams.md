{tgWebAppData: {…}, tgWebAppPlatform: 'tdesktop', tgWebAppThemeParams: {…}, tgWebAppVersion: '8.0'}
tgWebAppData
:
auth_date
:
Wed Jul 02 2025 22:55:21 GMT+0800 (中国标准时间) {}
chat_instance
:
"6891755669185758424"
chat_type
:
"private"
hash
:
"885a645ff5ae7d09503c7257dd50a4855774fd97565a996bbcf3db05c9ad5e50"
signature
:
"yVe56i6yls6EuF_I0s9OFQ5bgtywjksPOItu_aKoZ21e9ckmvSED7Gl8iuP3XjRZ8IG4fyrKsyIvtERNk_ayAw"
user
:
allows_write_to_pm
:
true
first_name
:
"cipherX"
id
:
6662549305
language_code
:
"zh-hans"
last_name
:
"\_ZH"
photo_url
:
"https://t.me/i/userpic/320/Ddepy63fuyaEFYJihlJAK7hLpZg8faemv1v9KHbOpG0qfZbxe2eWH3aZRRmhCDFi.svg"
username
:
"cipherX_ZH_admin"
[[Prototype]]
:
Object
[[Prototype]]
:
Object
tgWebAppPlatform
:
"tdesktop"
tgWebAppThemeParams
:
accent_text_color
:
"#6ab2f2"
bg_color
:
"#17212b"
bottom_bar_bg_color
:
"#17212b"
button_color
:
"#5288c1"
button_text_color
:
"#ffffff"
destructive_text_color
:
"#ec3942"
header_bg_color
:
"#17212b"
hint_color
:
"#708499"
link_color
:
"#6ab3f3"
secondary_bg_color
:
"#232e3c"
section_bg_color
:
"#17212b"
section_header_text_color
:
"#6ab3f3"
section_separator_color
:
"#111921"
subtitle_text_color
:
"#708499"
text_color
:
"#f5f5f5"
[[Prototype]]
:
Object
tgWebAppVersion
:
"8.0"
[[Prototype]]
:
Object

这是 官方sdk的 retrieveLaunchParams() 方法的返回值, 原先的判断和验证和取数据的方式都太冗余了,都想着是取parse InitData的 字符串, 请改造为官方SDK的方法
