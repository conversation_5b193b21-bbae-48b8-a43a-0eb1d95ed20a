Skipping initData hash validation based on DEV_SKIP_AUTH_VALIDATION flag in .env.
Error during initData validation: se [ValiError]: Invalid key: Expected "signature" but received undefined
at B (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:469:11)
at Object.operation (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:539:9)
at ~run (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:121:29)
at ~run (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:487:78)
at ~run (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:487:78)
at B (file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:467:22)
at file:///D:/dev-storage/pnpm/store/v10/links/@telegram-apps/transformers/2.2.6/2a37e15f3d8d2712e562549e799d3bf8da005baefe2c2085a259a0043fb23e9b/node_modules/@telegram-apps/transformers/dist/index.js:517:20
at validateAndParseInitData (D:/dev/TMA/blueX/src/lib/server/services/auth.service.ts:22:62)
at login (D:/dev/TMA/blueX/src/routes/+page.server.ts:17:80)
at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
issues: [
{
kind: 'schema',
type: 'loose_object',
input: undefined,
expected: '"signature"',
received: 'undefined',
message: 'Invalid key: Expected "signature" but received undefined',
requirement: undefined,
path: [Array],
issues: undefined,
lang: undefined,
abortEarly: undefined,
abortPipeEarly: undefined
}
]
}
