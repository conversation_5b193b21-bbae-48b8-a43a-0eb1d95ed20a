/**
 * @file Root Page - Server Actions
 * @description 处理应用的自动登录流程，现在基于 `launchParams`。
 */

import { fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';
import { lucia } from '$lib/server/auth';

// 导入更新后的服务模块
import { validateAndParseLaunchParams } from '$lib/server/services/auth.service';
import { findOrCreateUser } from '$lib/server/services/user.service';
import { isInitialOnboardingRequired } from '$lib/utils/user.utils';

export const actions: Actions = {
	login: async ({ request, cookies }) => {
		const formData = await request.formData();
		const launchParamsRaw = formData.get('launchParams') as string | undefined;

		if (!launchParamsRaw) {
			return fail(400, { message: 'Launch parameters are missing.' });
		}

		let launchParams: unknown;
		try {
			launchParams = JSON.parse(launchParamsRaw);
		} catch (e) {
			return fail(400, { message: 'Invalid launch parameters format.' });
		}

		// 1. 调用核心安全服务，完成所有 launchParams 的校验
		const validationResult = validateAndParseLaunchParams(launchParams);
		if (!validationResult.success) {
			return fail(403, { message: `Authentication failed: ${validationResult.error}` });
		}

		const { user: telegramUser, startParam } = validationResult.data;
		if (!telegramUser) {
			return fail(400, { message: 'User data is missing in launch parameters.' });
		}

		try {
			// 2. 调用用户服务，查找或创建用户
			const userInDb = await findOrCreateUser(telegramUser, startParam);

			// 3. 创建会话并设置 cookie
			const session = await lucia.createSession(userInDb.id, {});
			const sessionCookie = lucia.createSessionCookie(session.id);
			cookies.set(sessionCookie.name, sessionCookie.value, {
				path: '.',
				...sessionCookie.attributes
			});

			// 4. 判断是否需要进入引导流程
			const userNeedsOnboarding = isInitialOnboardingRequired(userInDb);

			// 5. 重定向到相应页面
			throw redirect(303, userNeedsOnboarding ? '/onboarding' : '/discover');
		} catch (e) {
			console.error('Login action failed during user processing:', e);
			return fail(500, { message: 'Internal server error.' });
		}
	}
};