// src/lib/utils/unit-conversions.ts
// CipherX TMA - 单位转换工具函数
// 职责：处理身高体重的国际化单位转换

// --- 核心常量与类型定义 ---

/**
 * 使用英制单位的国家代码
 */
const IMPERIAL_COUNTRIES = ['US', 'LR', 'MM'] as const;

// 核心转换率定义，作为唯一事实来源
const INCH_TO_CM = 2.54;
const KG_TO_LBS = 2.20462; // 更直观的命名，或使用 LBS_PER_KG = 0.453592

/**
 * 身高单位类型
 */
export type HeightUnit = 'cm' | 'ft-in';

/**
 * 体重单位类型
 */
export type WeightUnit = 'kg' | 'lbs';

/**
 * 英尺英寸表示
 */
export interface FeetInches {
	feet: number;
	inches: number;
	totalInches: number; // 从版本 B 引入
}

// --- 核心判断与转换函数 ---

/**
 * 根据国家代码判断是否应默认使用英制单位
 */
export function isImperialCountry(countryCode?: string | null): boolean {
	// 允许 null
	if (!countryCode) return false;
	return (IMPERIAL_COUNTRIES as readonly string[]).includes(countryCode.toUpperCase()); // 更好的类型断言
}

/**
 * 根据国家代码获取默认身高单位
 */
export function getDefaultHeightUnit(countryCode?: string | null): HeightUnit {
	// 允许 null
	return isImperialCountry(countryCode) ? 'ft-in' : 'cm';
}

/**
 * 根据国家代码获取默认体重单位
 */
export function getDefaultWeightUnit(countryCode?: string | null): WeightUnit {
	// 允许 null
	return isImperialCountry(countryCode) ? 'lbs' : 'kg';
}

// === 身高转换函数 ===

/**
 * 厘米转换为英尺英寸对象
 */
export function cmToFeetInches(cm: number): FeetInches {
	const totalInchesVal = cm / INCH_TO_CM;
	const feet = Math.floor(totalInchesVal / 12);
	let inches = Math.round(totalInchesVal % 12);

	if (inches === 12) {
		return { feet: feet + 1, inches: 0, totalInches: Math.round(totalInchesVal) };
	}
	return { feet, inches, totalInches: Math.round(totalInchesVal) };
}

/**
 * 英尺英寸对象转换为厘米
 */
export function feetInchesToCm({ feet, inches }: Partial<FeetInches>): number {
	// 接受 Partial<FeetInches>
	const totalInches = (feet || 0) * 12 + (inches || 0);
	return Math.round(totalInches * INCH_TO_CM);
}

/**
 * 格式化英尺英寸对象的显示字符串
 */
export function formatFeetInches(feetInches: FeetInches): string {
	return `${feetInches.feet}' ${feetInches.inches}"`; // 额外的空格
}

/**
 * 从厘米值获取格式化的英尺英寸字符串 (保留此函数，因为它很方便)
 */
export function formatCmAsFeetInches(cm: number): string {
	return formatFeetInches(cmToFeetInches(cm));
}

// === 体重转换函数 ===

/**
 * 千克转换为磅
 */
export function kgToLbs(kg: number): number {
	return Math.round(kg * KG_TO_LBS);
}

/**
 * 磅转换为千克
 */
export function lbsToKg(lbs: number): number {
	return Math.round(lbs / KG_TO_LBS);
}

// === 范围转换函数 (从版本 A 引入，并使用常量) ===

/**
 * 将厘米范围转换为英尺英寸的总英寸范围
 * 用于slider的min/max值
 */
export function cmRangeToInchesRange(minCm: number, maxCm: number): { min: number; max: number } {
	return {
		min: Math.floor(minCm / INCH_TO_CM),
		max: Math.ceil(maxCm / INCH_TO_CM)
	};
}

/**
 * 将总英寸转换为厘米 (从版本 A 引入，并使用常量)
 */
export function totalInchesToCm(totalInches: number): number {
	return Math.round(totalInches * INCH_TO_CM);
}

/**
 * 将厘米转换为总英寸 (从版本 A 引入，并使用常量)
 */
export function cmToTotalInches(cm: number): number {
	return Math.round(cm / INCH_TO_CM);
}

/**
 * 将千克范围转换为磅范围 (从版本 A 引入，并使用常量)
 */
export function kgRangeToLbsRange(minKg: number, maxKg: number): { min: number; max: number } {
	return {
		min: Math.floor(minKg * KG_TO_LBS),
		max: Math.ceil(maxKg * KG_TO_LBS)
	};
}

// === 验证函数 (从版本 A 引入) ===

/**
 * 验证英尺英寸值是否合理
 */
export function isValidFeetInches(feet: number, inches: number): boolean {
	return feet >= 0 && inches >= 0 && inches < 12;
}

/**
 * 验证身高厘米值是否在合理范围内
 */
export function isValidHeightCm(cm: number): boolean {
	return cm >= 100 && cm <= 250;
}

/**
 * 验证体重千克值是否在合理范围内
 */
export function isValidWeightKg(kg: number): boolean {
	return kg >= 30 && kg <= 300;
}

/**
 * 验证体重磅值是否在合理范围内
 */
export function isValidWeightLbs(lbs: number): boolean {
	const kg = lbsToKg(lbs);
	return isValidWeightKg(kg);
}
