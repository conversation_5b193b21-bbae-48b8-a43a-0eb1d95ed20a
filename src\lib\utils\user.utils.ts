// src/lib/utils/user.utils.ts
import type { DbUser, UserProfileData } from '$lib/schemas/profile.schema';
import type { User } from '../server/db/schema';

const MIN_BIO_LENGTH_FOR_BONUS = 50;
const MIN_RATINGS_FOR_BONUS = 3;

/**
 * 计算用户资料的完整度分数。
 * @param profile 用户资料对象，应为 UserProfileData 类型。
 * @returns 资料完整度分数 (0-100)。
 */
export function calculateProfileCompleteness(profile: Partial<UserProfileData>): number {
	if (!profile) return 0;

	let score = 0;

	// Level 0 - 基础分 (假设 ID 存在即有基础分)
	if (profile.id) {
		score += 10;
	}

	// Level 1 - 基础资料 (共50分)
	if (profile.nickname) score += 10;
	if (profile.age) score += 10;
	if (profile.heightCm) score += 5;
	if (profile.weightKg) score += 5;
	if (profile.bodyType) score += 10;
	if (profile.countryCode) score += 10;

	// Level 2 - 高级资料 (共40分)
	if (profile.orientation) score += 5;
	if (profile.presentationStyle) score += 5;
	if (profile.relationshipStatus) score += 5;
	if (profile.bio) {
		score += 5;
		if (profile.bio.length > MIN_BIO_LENGTH_FOR_BONUS) {
			score += 5; // 额外奖励
		}
	}
	if (profile.kinkCategoryBitmask && profile.kinkCategoryBitmask > 0) score += 5;
	if (profile.kinkRatings && Object.keys(profile.kinkRatings).length >= MIN_RATINGS_FOR_BONUS) {
		score += 10;
	}

	return Math.min(score, 100); // 确保分数不会超过100
}

/**
 * 判断用户是否需要强制进行初始 Onboarding。
 * 这通常用于新用户或资料极度不完整的用户。
 * @param user 用户资料对象。
 * @returns 如果需要强制 Onboarding，则返回 true，否则返回 false。
 */
export function isInitialOnboardingRequired(user: User): boolean {
	// 如果昵称缺失，或者资料完整度为0，则认为需要强制引导
	return !user.nickname || user.profileCompletenessScore === 0;
}

/**
 * 判断用户资料完整度是否达到Onboarding门槛（例如，解锁某些功能）。
 * @param user 用户资料对象。
 * @returns 如果资料完整度分数达到50分或以上，则返回 true，否则返回 false。
 */
export function hasMetOnboardingThreshold(user: UserProfileData): boolean {
	// 直接使用数据库中存储的 profileCompletenessScore
	return user.profileCompletenessScore >= 50;
}