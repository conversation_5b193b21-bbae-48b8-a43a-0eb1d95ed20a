/**
 * @file CipherX TMA - 认证相关业务流程验证规则
 * @version 4.0 (LaunchParams Aligned)
 * @description 本文件定义了与认证相关的、类型安全的数据契约，完全对齐 @telegram-apps/sdk-svelte 的 `retrieveLaunchParams()` 返回值。
 */

import { z } from 'zod';
import {
	telegramUserIdSchema,
	telegramUsernameSchema,
	languageCodeSchema,
	urlSchema
} from '$lib/schemas/common.schema';

/**
 * 内部 schema，用于验证 `LaunchParams` 中的 `user` 对象。
 * 字段已全部对齐 camelCase。
 */
const telegramUserSchema = z.object({
	id: telegramUserIdSchema,
	firstName: z.string(),
	lastName: z.string().optional(),
	username: telegramUsernameSchema.optional(),
	languageCode: languageCodeSchema.optional(),
	isPremium: z.boolean().optional(),
	photoUrl: urlSchema.optional()
});

/**
 * 核心 schema，用于验证从 `retrieveLaunchParams()` 返回的整个对象。
 * 这是我们后端服务层进行业务验证的唯一数据源。
 */
export const launchParamsSchema = z.object({
	initDataRaw: z.string().min(20, 'error_initdata_incomplete'),
	initData: z.object({
		hash: z.string().min(1, 'error_field_required'),
		authDate: z.date(),
		user: telegramUserSchema,
		startParam: z.string().optional(),
		queryId: z.string().optional(),
		chatType: z.string().optional(),
		chatInstance: z.string().optional()
	}),
	platform: z.string(),
	themeParams: z.any(), // themeParams 结构复杂且非核心业务，暂不详细验证
	version: z.string()
});

/**
 * 登录 API 端点 (/api/auth/telegram) 的请求体验证 schema。
 * 它现在需要接收一个符合 `launchParamsSchema` 的对象。
 */
export const loginApiPayloadSchema = launchParamsSchema;

// --- 类型导出 ---
export type LaunchParamsValidated = z.infer<typeof launchParamsSchema>;
export type LoginApiPayload = z.infer<typeof loginApiPayloadSchema>;
export type TelegramUserFromSDK = z.infer<typeof telegramUserSchema>;