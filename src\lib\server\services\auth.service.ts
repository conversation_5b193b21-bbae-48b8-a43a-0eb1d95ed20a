/**
 * @file CipherX TMA - 核心认证服务
 * @version 5.0 (LaunchParams-centric)
 * @description 提供一个全应用统一的、安全的 Telegram LaunchParams 验证与解析服务。
 *              此服务现在以 `LaunchParams` 对象为核心，取代了旧的 initData 字符串处理方式。
 */

import { BOT_TOKEN } from '$env/static/private';
import { validate, ExpiredError } from '@telegram-apps/init-data-node';
import { launchParamsSchema, type LaunchParamsValidated } from '$lib/schemas/auth.schema';

// 定义清晰的错误码
export type ValidationError =
	| 'INVALID_HASH'
	| 'AUTH_DATE_TOO_OLD'
	| 'INVALID_DATA_FORMAT'
	| 'MISSING_BOT_TOKEN';

// 定义服务返回的统一结果类型
type ValidationResult =
	| { success: true; data: LaunchParamsValidated['initData'] }
	| { success: false; error: ValidationError };

const AUTH_DATE_TOLERANCE_SECONDS = 3600; // 1 hour

/**
 * 安全地验证、解析并校验来自 Telegram Mini App 的 LaunchParams 对象。
 * 这是整个认证流程的核心安全函数。
 * @param launchParams - 从客户端 `retrieveLaunchParams()` 获取的完整对象。
 * @returns 包含成功数据(仅initData部分)或失败错误码的结果对象。
 */
export function validateAndParseLaunchParams(launchParams: unknown): ValidationResult {
	if (!BOT_TOKEN) {
		console.error('FATAL: TELEGRAM_BOT_TOKEN is not configured in .env file.');
		return { success: false, error: 'MISSING_BOT_TOKEN' };
	}

	// 1. 使用 Zod 对整个 launchParams 对象的结构和类型进行初步验证
	const zodResult = launchParamsSchema.safeParse(launchParams);
	if (!zodResult.success) {
		console.error('Zod validation failed for launchParams:', zodResult.error);
		return { success: false, error: 'INVALID_DATA_FORMAT' };
	}

	const { initDataRaw, initData } = zodResult.data;

	// 2. 对原始 initData 字符串执行加密哈希验证
	try {
		validate(initDataRaw, BOT_TOKEN, {
			expiresIn: AUTH_DATE_TOLERANCE_SECONDS
		});

		// 验证成功，直接返回已经由 Zod 解析和验证过的 initData 部分
		return { success: true, data: initData };
	} catch (e: unknown) {
		console.error('Cryptographic validation failed for initData:', e);

		if (e instanceof ExpiredError) {
			return { success: false, error: 'AUTH_DATE_TOO_OLD' };
		}

		// 其他验证错误（如无效哈希）
		return { success: false, error: 'INVALID_HASH' };
	}
}
