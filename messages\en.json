{"$schema": "https://inlang.com/schema/inlang-message-format", "app_title": "CipherX", "common_greeting": "Hello, {name} from en!", "common_button_save_and_continue": "Save and Continue", "common_placeholder_select": "Please select...", "common_state_saving": "Saving...", "common_units_height_cm": "cm", "common_units_height_ft_in": "ft/in", "common_units_weight_kg": "kg", "common_units_weight_lbs": "lbs", "common_tooltip_toggle_unit": "Switch units", "components_country_selector_placeholder_country": "Select a country...", "components_country_selector_placeholder_province": "Select a state/province...", "lib_validation_id_required": "ID is required.", "lib_validation_id_prefix_invalid": "Invalid ID format.", "lib_validation_telegram_id_required": "Telegram User ID is required.", "lib_validation_telegram_id_invalid": "Invalid Telegram User ID.", "lib_validation_kink_map_code_required": "Kink Map Code is required.", "lib_validation_kink_map_code_length": "Kink Map Code must be 8 characters long.", "lib_validation_nickname_required": "Nickname is required.", "lib_validation_nickname_too_short": "Nickname must be at least 2 characters.", "lib_validation_nickname_too_long": "Nickname cannot exceed 50 characters.", "lib_validation_telegram_username_format": "Username can only contain letters, numbers, and underscores.", "lib_validation_bio_too_long": "Bio cannot exceed 500 characters.", "lib_validation_age_must_be_number": "Age must be a number.", "lib_validation_age_must_be_integer": "Age must be a whole number.", "lib_validation_age_too_young": "You must be at least 18 years old.", "lib_validation_age_invalid": "Please enter a valid age.", "lib_validation_height_must_be_number": "Height must be a number.", "lib_validation_height_out_of_range": "Please enter a valid height (100-250 cm).", "lib_validation_weight_must_be_number": "Weight must be a number.", "lib_validation_weight_out_of_range": "Please enter a valid weight (30-300 kg).", "lib_validation_country_code_invalid": "Invalid country code.", "lib_validation_city_required": "City name is required.", "lib_validation_ton_address_invalid": "Invalid TON wallet address format.", "lib_validation_rating_invalid": "Invalid rating value.", "lib_validation_slug_format": "Slug can only contain lowercase letters, numbers, and hyphens.", "lib_validation_slug_no_hyphen_ends": "Slug cannot start or end with a hyphen.", "lib_validation_slug_too_short": "Slug must be at least 3 characters long.", "lib_validation_slug_too_long": "Slug cannot exceed 100 characters.", "lib_validation_invitee_identifier_required": "Either a user ID or a Telegram user ID for the invitee must be provided.", "lib_validation_initdata_incomplete": "Authentication data is incomplete. Key fields are missing.", "lib_validation_initdata_parse_failed": "Authentication data is malformed and cannot be parsed.", "lib_validation_invite_code_length": "Invite code must be exactly 8 characters long.", "routes_onboarding_title": "Complete Your Profile", "routes_onboarding_description": "To provide you with the best experience, we need to know a little bit about you. This information will be used to personalize your journey.", "routes_onboarding_basic_profile_title": "Basic Profile", "routes_onboarding_basic_profile_description": "Let's start with some basic information about you.", "routes_onboarding_physical_chars_title": "Physical Characteristics", "routes_onboarding_location_title": "Location", "routes_onboarding_label_nickname": "Nickname", "routes_onboarding_placeholder_nickname": "Enter your nickname", "routes_onboarding_label_age": "Age", "routes_onboarding_label_height": "Height", "routes_onboarding_label_weight": "Weight", "routes_onboarding_label_body_type": "Body Type", "routes_onboarding_placeholder_body_type": "Select your body type...", "routes_onboarding_label_country": "Country", "routes_onboarding_label_province": "Province / State", "routes_onboarding_label_city": "City", "routes_onboarding_placeholder_city": "Enter your city", "routes_onboarding_button_submit": "Save and Continue", "routes_onboarding_checkbox_privacy_terms": "I accept the Privacy Policy and Terms of Service", "routes_onboarding_button_show_advanced": "Show Advanced Profile Options", "routes_onboarding_advanced_profile_title": "Advanced Profile", "routes_onboarding_label_orientation": "Sexual Orientation", "routes_onboarding_label_presentation_style": "Presentation Style", "routes_onboarding_label_relationship_status": "Relationship Status", "routes_onboarding_label_bio": "Bio", "routes_onboarding_placeholder_bio": "Tell us about yourself...", "routes_onboarding_label_kink_roles": "<PERSON><PERSON>s", "routes_onboarding_label_kink_interests": "<PERSON><PERSON>s", "data_kink_role_DOMINANT": "Dominant", "data_kink_role_SUBMISSIVE": "Submissive", "data_kink_role_SWITCH_DS": "Switch (D/s)", "data_kink_role_TOP": "Top", "data_kink_role_BOTTOM": "Bottom", "data_kink_role_VERSATILE": "Versatile", "data_kink_role_SIDE": "Side", "data_kink_role_EDGEE": "<PERSON><PERSON>", "data_kink_role_EDGER": "<PERSON><PERSON>", "data_kink_role_EDGE_SWITCHER": "Switch (Edge)", "data_kink_interest_sight": "Visual", "data_kink_interest_sound": "Auditory", "data_kink_interest_smell": "Olfactory", "data_kink_interest_mouth": "Oral", "data_kink_interest_nipple_play": "Nipple Play", "data_kink_interest_hand_play": "Hand Play", "data_kink_interest_chastity": "Chastity", "data_kink_interest_genital_play": "Genital Play", "data_kink_interest_buttock_play": "Buttock Play", "data_kink_interest_anal_play": "Anal Play", "data_kink_interest_foot_fetish": "<PERSON>", "data_kink_interest_service": "Service", "data_kink_interest_role_playing": "Role Playing", "data_kink_interest_public_play": "Public Play", "data_kink_interest_online": "Online / Remote", "data_kink_interest_tool_play": "Tool / Toy Play", "data_kink_rating_label_minus_2": "Obediently if required", "data_kink_rating_label_minus_1": "Hard Limit / No", "data_kink_rating_label_0": "Neutral / Indifferent", "data_kink_rating_label_1": "S<PERSON>ly <PERSON>ous", "data_kink_rating_label_2": "Interested", "data_kink_rating_label_3": "Like It", "data_kink_rating_label_4": "Love It", "data_kink_rating_label_5": "Favorite / Essential", "data_group_theme_men_seeking_men": "Rainbow Men", "data_group_theme_women_seeking_women": "Rainbow Women", "data_group_theme_all_inclusive_community": "CipherX Community", "data_body_type_male": "Male Body", "data_body_type_female": "Female Body", "data_body_type_other": "Other", "data_orientation_straight": "Straight", "data_orientation_gay": "<PERSON>", "data_orientation_lesbian": "Lesbian", "data_orientation_bisexual": "Bisexual", "data_orientation_asexual": "Asexual", "data_orientation_demisexual": "Demisexual", "data_orientation_pansexual": "Pansexual", "data_orientation_queer": "Queer", "data_orientation_fluid": "Fluid", "data_orientation_other": "Other", "data_orientation_prefer_not_to_say": "Prefer Not to Say", "data_presentation_style_conventional_masculine": "Conventional Masculine", "data_presentation_style_feminine": "Feminine", "data_presentation_style_androgynous_neutral": "Androgynous / Neutral", "data_presentation_style_other": "Other", "data_relationship_status_single": "Single", "data_relationship_status_in_a_relationship": "In a Relationship", "data_relationship_status_complicated": "Complicated", "data_relationship_status_open_relationship": "Open Relationship", "data_relationship_status_married": "Married", "data_relationship_status_polyamorous": "<PERSON><PERSON><PERSON>", "data_relationship_status_other": "Other", "data_relationship_status_prefer_not_to_say": "Prefer Not to Say", "lib_validation_initdata_not_found": "Could not retrieve user authentication data. Please try again.", "routes_login_authenticating_message": "Authenticating...", "common_error_unauthorized": "Unauthorized. Please log in again.", "common_error_profile_save_failed": "Failed to save profile. Please try again later.", "data_presentation_style_rugged_masculine": "Rugged Mas<PERSON>line"}