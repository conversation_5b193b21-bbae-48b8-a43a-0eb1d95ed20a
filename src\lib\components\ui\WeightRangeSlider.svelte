<!-- src/lib/components/ui/WeightRangeSlider.svelte -->
<script lang="ts">
	import { Range, ListItem, BlockTitle, Button } from 'konsta/svelte';
	import type { ComponentProps } from 'svelte';
	import * as m from '$lib/paraglide/messages';
	import {
		type WeightUnit,
		getDefaultWeightUnit,
		kgToLbs,
		lbsToKg,
		kgRangeToLbsRange,
		isValidWeightKg,
		isValidWeightLbs
	} from '$lib/utils/unit-conversions';

	type Props = {
		value?: number; // 始终以kg为单位
		label: string;
		countryCode?: string;
		min?: number; // kg
		max?: number; // kg
		disabled?: boolean;
	} & Omit<ComponentProps<Range>, 'value' | 'min' | 'max' | 'step'>;

	let {
		value = $bindable(70), // 默认70kg
		label,
		countryCode,
		min = 40,
		max = 150,
		disabled = false,
		...restProps
	}: Props = $props();

	// 内部状态
	let currentUnit = $state<WeightUnit>(getDefaultWeightUnit(countryCode));
	let sliderValue = $state(0);

	// 根据当前单位计算slider的范围和值
	let sliderConfig = $derived.by(() => {
		if (currentUnit === 'kg') {
			return {
				min,
				max,
				step: 1,
				value: value,
				unit: m.common_units_weight_kg()
			};
		} else {
			// 磅模式
			const lbsRange = kgRangeToLbsRange(min, max);
			return {
				min: lbsRange.min,
				max: lbsRange.max,
				step: 1,
				value: kgToLbs(value),
				unit: m.common_units_weight_lbs()
			};
		}
	});

	// 显示值
	let displayValue = $derived(() => {
		if (currentUnit === 'kg') {
			return `${value}${m.common_units_weight_kg()}`;
		} else {
			return `${kgToLbs(value)}${m.common_units_weight_lbs()}`;
		}
	});

	// 同步slider值到实际值
	$effect(() => {
		if (currentUnit === 'kg') {
			sliderValue = value;
		} else {
			sliderValue = kgToLbs(value);
		}
	});

	// 处理slider值变化
	function handleSliderChange(newSliderValue: number) {
		if (currentUnit === 'kg') {
			if (isValidWeightKg(newSliderValue)) {
				value = newSliderValue;
			}
		} else {
			// 从磅转换为kg
			const newKg = lbsToKg(newSliderValue);
			if (isValidWeightKg(newKg)) {
				value = newKg;
			}
		}
	}

	// 切换单位
	function toggleUnit() {
		currentUnit = currentUnit === 'kg' ? 'lbs' : 'kg';
	}

	// 格式化范围显示
	function formatRangeValue(val: number): string {
		if (currentUnit === 'kg') {
			return val.toString();
		} else {
			return val.toString();
		}
	}
</script>

<div>
	<BlockTitle class="flex items-center justify-between">
		<span>{label}</span>
		<div class="flex items-center gap-2">
			<span class="font-semibold">{displayValue}</span>
			<Button
				small
				outline
				class="min-w-16 text-xs"
				title={m.common_tooltip_toggle_unit()}
				{disabled}
				onclick={toggleUnit}
			>
				{currentUnit === 'kg' ? m.common_units_weight_lbs() : m.common_units_weight_kg()}
			</Button>
		</div>
	</BlockTitle>
	<ListItem innerClass="flex space-x-4 rtl:space-x-reverse">
		<span class="text-sm opacity-50">
			{formatRangeValue(sliderConfig.min)}
		</span>
		<Range
			{...restProps}
			min={sliderConfig.min}
			max={sliderConfig.max}
			step={sliderConfig.step}
			value={sliderConfig.value}
			{disabled}
			oninput={(e) => {
				const target = e.target as HTMLInputElement;
				handleSliderChange(Number(target.value));
			}}
		/>
		<span class="text-sm opacity-50">
			{formatRangeValue(sliderConfig.max)}
		</span>
	</ListItem>
</div>
