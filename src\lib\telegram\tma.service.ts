// src/lib/telegram/tma.service.ts
/**
 * @file CipherX TMA - Telegram Mini App SDK 服务
 * @version 5.1 (Production-Ready)
 * @description 本文件是应用与 Telegram SDK 交互的唯一、统一的入口点。
 *              它只负责初始化SDK，并安全地获取真实的 launchParams。
 */

import {
	init,
	retrieveLaunchParams,
	mainButton,
	backButton,
	viewport,
	hapticFeedback,
	themeParams,
	type LaunchParams
} from '@telegram-apps/sdk-svelte';

import { writable, type Writable } from 'svelte/store';

// --- 重新导出官方的组件和信号，供整个应用使用 ---
export { mainButton, backButton, viewport, hapticFeedback, themeParams };

/**
 * 一个 Writable store，用于安全地存储从 SDK 获取的启动参数。
 * UI 和业务逻辑应该订阅此 store 来获取数据。
 * 它的值在 `initializeTma` 中被设置一次。
 * 设置为 Writable 是为了方便在初始化时赋值，但不建议在应用其他地方写入。
 */
export const launchParams: Writable<Partial<LaunchParams>> = writable({});

/**
 * 一个简单的布尔值 store，用于在 UI 中轻松判断 SDK 是否已就绪。
 */
export const tmaReady = writable<boolean>(false);

let isInitialized = false;

/**
 * 唯一的TMA SDK初始化入口函数。
 */
export function initializeTma(): () => void {
	if (typeof window === 'undefined' || isInitialized) {
		return () => {};
	}
	isInitialized = true;

	try {
		const cleanup = init({
			acceptCustomStyles: true
		});

		viewport.expand();

		// 获取启动参数并更新 store
		const params = retrieveLaunchParams();
		launchParams.set(params);

		tmaReady.set(true);
		console.log('✅ Telegram Mini App SDK initialized correctly.');
		return cleanup;
	} catch (e: unknown) {
		const error = e instanceof Error ? e : new Error(String(e));
		console.error('❌ Failed to initialize TMA SDK:', error);
		launchParams.set({}); // 出错时设置为空对象
		tmaReady.set(true);
		return () => {};
	}
}