# Internationalization (i18n) Key Naming Guidelines

To ensure consistency, scalability, and ease of maintenance, all internationalization keys must follow a standardized naming convention.

## Naming Convention

We will use a **scope-based, snake_case** naming convention. This approach provides a clear, structured, and easily searchable format for all keys.

### Structure

The format for each key is as follows:

`scope_feature_element_property`

### Components of the Key

1.  **`scope` (Required)**
    *   Defines the top-level directory or area where the key is primarily used. This helps in quickly locating the relevant code.
    *   **Values:**
        *   `routes`: For text specific to a page or a set of pages (e.g., `src/routes/onboarding`).
        *   `components`: For reusable UI components (e.g., `src/lib/components/ui/CountrySelector.svelte`).
        *   `lib`: For text originating from utility functions or services (e.g., `src/lib/utils`, `src/lib/server`).
        *   `common`: For globally shared text that doesn't fit into a specific category (e.g., units, generic buttons like "Save").

2.  **`feature` (Required)**
    *   Describes the specific feature, page, or component name.
    *   Use snake_case for multi-word features (e.g., `user_profile`, `country_selector`).
    *   **Examples:** `onboarding`, `discover`, `country_selector`, `height_range_slider`.

3.  **`element` (Required)**
    *   Describes the UI element type or the category of the text.
    *   **Examples:** `title`, `subtitle`, `button`, `label`, `placeholder`, `tooltip`, `error`, `success_message`, `validation`.

4.  **`property` (Optional)**
    *   Provides a more specific description of the element's purpose or content, especially when a feature has multiple similar elements.
    *   **Examples:** `submit`, `cancel`, `country`, `province`, `username`.

---

### Examples

| Key | Scope | Feature | Element | Property | Description |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `routes_onboarding_welcome_title` | `routes` | `onboarding` | `title` | `welcome` | The main welcome title on the onboarding page. |
| `components_country_selector_placeholder_country` | `components` | `country_selector` | `placeholder` | `country` | Placeholder text for the country selection input. |
| `components_country_selector_placeholder_province` | `components` | `country_selector` | `placeholder` | `province` | Placeholder text for the province selection input. |
| `common_button_save` | `common` | `button` | `save` | | A generic "Save" button text. |
| `common_units_height_cm` | `common` | `units` | `height` | `cm` | The "cm" unit for height. |
| `lib_validation_error_email_invalid` | `lib` | `validation` | `error` | `email_invalid` | Validation error message for an invalid email format. |

### Best Practices

*   **Be Consistent:** Strictly adhere to this format for all new keys.
*   **Be Descriptive:** Choose names that are easy to understand.
*   **Lowercase Only:** All keys should be in lowercase to avoid case-sensitivity issues.
*   **Refactor When Necessary:** If a component's scope changes, update its i18n keys accordingly.
