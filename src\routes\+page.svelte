<script lang="ts">
	import { onMount, tick } from 'svelte';
	import { Preloader, Page } from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import { launchParams } from '$lib/telegram/tma.service';

	let formElement: HTMLFormElement;
	let launchParamsJson = $state('');
	let error = $state<string | null>(null);

	// Subscribe directly to the launchParams store.
	// This ensures the logic runs only when launchParams has been updated.
	onMount(() => {
		const unsubscribe = launchParams.subscribe(async (params) => {
			if (params && Object.keys(params).length > 0) {
				launchParamsJson = JSON.stringify(params);
				await tick(); // Wait for the DOM to update with the new value
				formElement?.requestSubmit();

				// Unsubscribe after successful submission to prevent memory leaks
				unsubscribe();
			}
		});

		// Set a timeout to handle cases where launchParams are never received.
		setTimeout(() => {
			if (!launchParamsJson) {
				error = m.lib_validation_initdata_not_found();
			}
		}, 5000); // 5-second timeout
	});
</script>

<Page>
	<form action="?/login" method="POST" bind:this={formElement} class="hidden">
		<input type="hidden" name="launchParams" bind:value={launchParamsJson} />
	</form>

	<div class="flex h-screen items-center justify-center p-4">
		{#if error}
			<div class="text-center text-red-500">{error}</div>
		{:else}
			<div class="text-center text-gray-500">
				<Preloader size="w-12 h-12" />
				<p class="mt-4">{m.routes_login_authenticating_message()}</p>
			</div>
		{/if}
	</div>
</Page>
