<!-- src/lib/components/ui/HeightRangeSlider.svelte -->
<script lang="ts">
	import { Range, ListItem, BlockTitle, Button } from 'konsta/svelte';
	import type { ComponentProps } from 'svelte';
	import * as m from '$lib/paraglide/messages';
	import {
		type HeightUnit,
		type FeetInches,
		getDefaultHeightUnit,
		cmToFeetInches,
		feetInchesToCm,
		formatFeetInches,
		cmToTotalInches,
		totalInchesToCm,
		cmRangeToInchesRange,
		isValidHeightCm
	} from '$lib/utils/unit-conversions';

	type Props = {
		value?: number; // 始终以cm为单位
		label: string;
		countryCode?: string;
		min?: number; // cm
		max?: number; // cm
		disabled?: boolean;
	} & Omit<ComponentProps<Range>, 'value' | 'min' | 'max' | 'step'>;

	let {
		value = $bindable(170), // 默认170cm
		label,
		countryCode,
		min = 140,
		max = 220,
		disabled = false,
		...restProps
	}: Props = $props();

	// 内部状态
	let currentUnit = $state<HeightUnit>(getDefaultHeightUnit(countryCode));
	let sliderValue = $state(0);

	// 根据当前单位计算slider的范围和值
	let sliderConfig = $derived.by(() => {
		let sliderConfig;
		if (currentUnit === 'cm') {
			sliderConfig = {
				min,
				max,
				step: 1,
				value: value,
				unit: m.common_units_height_cm()
			};
		} else {
			// 英尺英寸模式：使用总英寸作为slider值
			const inchesRange = cmRangeToInchesRange(min, max);
			sliderConfig = {
				min: inchesRange.min,
				max: inchesRange.max,
				step: 1,
				value: cmToTotalInches(value),
				unit: m.common_units_height_ft_in()
			};
		}
		return sliderConfig;
	});

	// 显示值
	let displayValue = $derived(() => {
		if (currentUnit === 'cm') {
			return `${value}${m.common_units_height_cm()}`;
		} else {
			const feetInches = cmToFeetInches(value);
			return formatFeetInches(feetInches);
		}
	});

	// 同步slider值到实际值
	$effect(() => {
		if (currentUnit === 'cm') {
			sliderValue = value;
		} else {
			sliderValue = cmToTotalInches(value);
		}
	});

	// 处理slider值变化
	function handleSliderChange(newSliderValue: number) {
		if (currentUnit === 'cm') {
			value = newSliderValue;
		} else {
			// 从总英寸转换为cm
			const newCm = totalInchesToCm(newSliderValue);
			if (isValidHeightCm(newCm)) {
				value = newCm;
			}
		}
	}

	// 切换单位
	function toggleUnit() {
		currentUnit = currentUnit === 'cm' ? 'ft-in' : 'cm';
	}

	// 格式化范围显示
	function formatRangeValue(val: number): string {
		if (currentUnit === 'cm') {
			return val.toString();
		} else {
			const feetInches = cmToFeetInches(totalInchesToCm(val));
			return formatFeetInches(feetInches);
		}
	}
</script>

<div>
	<BlockTitle class="flex items-center justify-between">
		<span>{label}</span>
		<div class="flex items-center gap-2">
			<span class="font-semibold">{displayValue}</span>
			<Button
				small
				outline
				class="min-w-16 text-xs"
				title={m.common_tooltip_toggle_unit()}
				{disabled}
				onclick={toggleUnit}
			>
				{currentUnit === 'cm' ? m.common_units_height_ft_in() : m.common_units_height_cm()}
			</Button>
		</div>
	</BlockTitle>
	<ListItem innerClass="flex space-x-4 rtl:space-x-reverse">
		<span class="text-sm opacity-50">
			{formatRangeValue(sliderConfig.min)}
		</span>
		<Range
			{...restProps}
			min={sliderConfig.min}
			max={sliderConfig.max}
			step={sliderConfig.step}
			value={sliderConfig.value}
			{disabled}
			onInput={(e) => {
				const target = e.target as HTMLInputElement;
				handleSliderChange(Number(target.value));
			}}
		/>
		<span class="text-sm opacity-50">
			{formatRangeValue(sliderConfig.max)}
		</span>
	</ListItem>
</div>
