/**
 * @file Onboarding - Server Logic
 * @description 处理新用户资料填写的表单加载与数据提交 (v3.0 - Self-Contained)
 */

import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import * as m from '$lib/paraglide/messages';

import { onboardingFormSchema } from '$lib/schemas/profile.schema';
import { getFullUserProfile, updateUserProfile } from '$lib/server/services/user.service';
import { bodyTypeEnum } from '$lib/server/db/schema';

// --- Load Function: 准备表单数据 ---
export const load: PageServerLoad = async ({ locals }) => {
	// ✅ 关键修正：直接从 `locals` 中获取完整的、未经削减的用户对象。
	// `locals.user` 是由 hooks.server.ts 提供的、直接来自数据库的完整用户模型。
	const { user } = locals;

	// 安全守卫��如果用户未登录，直接踢回首页
	if (!user) {
		throw redirect(303, '/');
	}

	// 尽管 `locals.user` 已经很完整，但为了获取最新的数据（例如用户刚刚在其他设备上修改过），
	// 最佳实践仍然是重新查询一次数据库。
	const userProfileFromDb = await getFullUserProfile(user.id);

	// ✅ 核心逻辑：构建一个“自给自足”的、用于预填充的初始数据对象。
	// 数据来源的优先级:
	// 1. 数据库中最新的值 (userProfileFromDb)
	// 2. `locals.user` 中缓存的初始值 (例如 languageCode)
	// 3. 硬编码的、合理的默认值
	const initialData = {
		nickname: userProfileFromDb?.nickname ?? user.nickname,
		telegramUsername: user.telegramUsername,
		age: userProfileFromDb?.age ?? 20,
		heightCm: userProfileFromDb?.heightCm ?? 175,
		weightKg: userProfileFromDb?.weightKg ?? 65,
		countryCode: userProfileFromDb?.countryCode, // 让前端根据 languageCode 智能选择
		provinceCode: userProfileFromDb?.provinceCode,
		city: userProfileFromDb?.city,
		languageCode: user.languageCode,
		bodyType: userProfileFromDb?.bodyType
	};

	// 使用 onboardingFormSchema 来初始化表单，并传入智能预填充的数据
	const form = await superValidate(initialData, zod(onboardingFormSchema));

	// 将表单对象和 languageCode (用于国家选择器) 单独返回给前端页面
	return {
		form,
		languageCode: initialData.languageCode,
		bodyTypeOptions: bodyTypeEnum.enumValues
	};
};

// --- Actions: 处理表单提交 ---
export const actions: Actions = {
	default: async ({ request, locals }) => {
		const { user } = locals;
		if (!user) {
			return fail(401, { message: m.common_error_unauthorized() });
		}

		const form = await superValidate(request, zod(onboardingFormSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			await updateUserProfile(user.id, form.data);
		} catch (e: any) {
			console.error('Onboarding update failed:', e);
			return fail(500, { form, message: m.common_error_profile_save_failed() });
		}

		throw redirect(303, '/discover');
	}
};

